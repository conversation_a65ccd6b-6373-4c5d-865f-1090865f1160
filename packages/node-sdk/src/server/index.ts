/**
 * @file Server implementation for ezWorkflow
 *
 * This file provides the server implementation for ezWorkflow, which allows
 * users to expose their nodes, executors, and triggers through a Hono.js API.
 */

import { Hono } from "hono";
import { logger } from "hono/logger";
import { requestId } from "hono/request-id";
import { contextStorage } from "hono/context-storage";
import { timeout } from "hono/timeout";
import { cache } from "hono/cache";
import { compress } from "hono/compress";
import { bearerAuth } from "hono/bearer-auth";
import { Node, NodeExecutor, TriggerDefinition } from "../types";
import { componentRegistry } from "../registry";
import { PageDefinition } from "../defines/page";
import { DashboardWidgetDefinition } from "../defines/dashboard-widget";
import { ModalDefinition } from "../defines/modal";
import { DrawerDefinition } from "../defines/drawer";
import { EmailBuilderBlockDefinition } from "../defines/email-builder-block";
import { FunnelBuilderBlockDefinition } from "../defines/funnel-builder-block";
import {
  getConfig,
  hasConfig,
  type EzwConfig,
} from "@ezworkflow/project-config";

import { createRouter } from "./routes/index";
import { ServerContext } from "./routes/middleware";

/**
 * Options for the ezWorkflow server
 */
export interface ServerOptions {
  /**
   * API key for authentication
   * If provided, all requests must include this key in the Authorization header
   */
  apiKey?: string;

  /**
   * Permissions array defining what operations are allowed
   * Available permissions: 'read', 'execute', 'admin'
   * - 'read': Allow reading node, executor, trigger, and UI component information
   * - 'execute': Allow executing nodes
   * - 'admin': Allow all operations
   * If not provided, all operations are allowed
   */
  permissions?: string[];

  /**
   * App type
   * 'partner' or 'account'
   */
  appType?: "partner" | "account";

  /**
   * App version
   * The version of the app
   */
  version?: string;

  /**
   * App author
   * The author of the app
   */
  author?:
    | string
    | {
        name: string; // The name of the author
        email?: string; // The email of the author
        url?: string; // The URL of the author
        support?: string; // The support URL of the author
        logo?: string; // The logo of the author
      };

  /**
   * App categories
   * The categories of the app
   */
  categories?: string[];

  /**
   * App tags
   * The tags of the app
   */
  tags?: string[];

  /**
   * Whether to use project configuration from ezw.json
   * If true, will attempt to read configuration from ezw.json file
   * If false, will only use options provided to the constructor
   * Default: true
   */
  useProjectConfig?: boolean;

  /**
   * Middleware configuration options
   */
  middleware?: {
    /**
     * Enable request logging
     * Default: true
     */
    logger?: boolean;

    /**
     * Enable request ID generation
     * Default: true
     */
    requestId?: boolean;

    /**
     * Enable context storage
     * Default: true
     */
    contextStorage?: boolean;

    /**
     * Request timeout in milliseconds
     * Default: 29000 (29 seconds)
     */
    timeout?: number;

    /**
     * Enable response compression
     * Automatically disabled for Cloudflare Workers and Deno runtimes
     * Default: true
     */
    compress?: boolean;

    /**
     * Enable caching for supported runtimes (Cloudflare Workers, Deno)
     * Default: true
     */
    cache?: boolean;

    /**
     * Cache configuration
     */
    cacheConfig?: {
      cacheName?: string;
      cacheControl?: string;
    };
  };
}

/**
 * ezWorkflow server class
 *
 * This class provides a comprehensive server implementation for ezWorkflow, which allows
 * users to expose their nodes, executors, triggers, and UI components through a Hono.js API.
 * It serves as the main server for the ezWorkflow platform, handling all types of components
 * rather than just nodes.
 */
export class ezwServer {
  private app: Hono;
  private nodes: Map<string, Node> = new Map();
  private executors: Map<string, NodeExecutor> = new Map();
  private triggers: Map<string, TriggerDefinition> = new Map();
  private pages: Map<string, PageDefinition> = new Map();
  private modals: Map<string, ModalDefinition> = new Map();
  private dashboardWidgets: Map<string, DashboardWidgetDefinition> = new Map();
  private drawers: Map<string, DrawerDefinition> = new Map();
  private emailBuilderBlocks: Map<string, EmailBuilderBlockDefinition> =
    new Map();
  private funnelsBuilderBlocks: Map<string, FunnelBuilderBlockDefinition> =
    new Map();
  private apiKey?: string;
  private permissions: string[] = [];
  private options: ServerOptions = {};
  private projectConfig?: EzwConfig;
  private runtime?: string;

  /**
   * Create a new ezwServer instance
   *
   * @param options Server options
   */
  constructor(options: ServerOptions = {}) {
    this.app = new Hono();
    this.options = options;

    // Load project configuration if enabled
    this.loadProjectConfig();

    // Merge configuration with options (options take precedence)
    this.mergeConfiguration();

    // Set up enhanced middleware stack
    this.setupMiddleware();

    // Set up authentication if API key is provided
    if (this.apiKey) {
      this.setupAuthMiddleware();
    }

    // Use nodes from the registry
    for (const node of componentRegistry.getAllNodes()) {
      this.nodes.set(node.definition.id, node);
    }

    // Use executors from the registry
    const executors = componentRegistry.getAllExecutors();
    for (const executor of executors) {
      // We need to find the node ID for this executor
      // For now, we'll assume the executor ID is the same as the node ID
      for (const node of this.nodes.values()) {
        if (node.executor === executor) {
          this.executors.set(node.definition.id, executor);
          break;
        }
      }
    }

    // Use triggers from the registry
    for (const trigger of componentRegistry.getAllTriggers()) {
      this.triggers.set(trigger.id, trigger);
    }

    // Use UI components from the registry
    for (const page of componentRegistry.getAllPages()) {
      this.pages.set(page.id, page);
    }

    for (const modal of componentRegistry.getAllModals()) {
      this.modals.set(modal.id, modal);
    }

    for (const dashboardWidget of componentRegistry.getAllDashboardWidgets()) {
      this.dashboardWidgets.set(dashboardWidget.id, dashboardWidget);
    }

    for (const drawer of componentRegistry.getAllDrawers()) {
      this.drawers.set(drawer.id, drawer);
    }

    for (const emailBuilderBlock of componentRegistry.getAllEmailBuilderBlocks()) {
      this.emailBuilderBlocks.set(emailBuilderBlock.id, emailBuilderBlock);
    }

    for (const funnelBuilderBlock of componentRegistry.getAllFunnelBuilderBlocks()) {
      this.funnelsBuilderBlocks.set(funnelBuilderBlock.id, funnelBuilderBlock);
    }

    // Set up routes using modular approach
    this.setupModularRoutes();
  }

  /**
   * Load project configuration from ezw.json if available and enabled
   */
  private loadProjectConfig(): void {
    // Check if project configuration should be used
    if (this.options.useProjectConfig === false) {
      return;
    }

    try {
      if (hasConfig()) {
        this.projectConfig = getConfig();
        this.runtime = this.projectConfig.runtime;
      }
    } catch (error) {
      // Silently ignore configuration errors - the server can still work without project config
      console.warn(
        "Warning: Could not load project configuration:",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Merge project configuration with provided options
   * Options take precedence over project configuration
   */
  private mergeConfiguration(): void {
    if (this.projectConfig) {
      // Merge API key (options take precedence)
      this.apiKey = this.options.apiKey || this.projectConfig.apiKey;

      // Merge other configuration values
      if (!this.options.version && this.projectConfig.version) {
        this.options.version = this.projectConfig.version;
      }

      if (!this.options.author && this.projectConfig.author) {
        this.options.author = this.projectConfig.author;
      }

      if (!this.options.categories && this.projectConfig.categories) {
        this.options.categories = this.projectConfig.categories;
      }

      if (!this.options.tags && this.projectConfig.tags) {
        this.options.tags = this.projectConfig.tags;
      }

      if (!this.options.appType && this.projectConfig.appType) {
        this.options.appType = this.projectConfig.appType;
      }
    } else {
      // No project config, use options only
      this.apiKey = this.options.apiKey;
    }

    // Set permissions
    this.permissions = this.options.permissions || [];
  }

  /**
   * Set up enhanced middleware stack based on configuration and runtime
   */
  private setupMiddleware(): void {
    const middlewareConfig = this.options.middleware || {};

    // Request logging (enabled by default)
    if (middlewareConfig.logger !== false) {
      this.app.use(logger());
    }

    // Request ID generation (enabled by default)
    if (middlewareConfig.requestId !== false) {
      this.app.use(requestId());
    }

    // Context storage (enabled by default)
    if (middlewareConfig.contextStorage !== false) {
      this.app.use(contextStorage());
    }

    // Request timeout (default: 29 seconds)
    const timeoutMs = middlewareConfig.timeout || 29000;
    this.app.use(timeout(timeoutMs));

    // Runtime-specific middleware
    if (this.runtime === "cloudflare-workers" || this.runtime === "deno") {
      // Enable caching for Cloudflare Workers and Deno (if not disabled)
      if (middlewareConfig.cache !== false) {
        const cacheConfig = middlewareConfig.cacheConfig || {};
        this.app.get(
          "*",
          cache({
            cacheName: cacheConfig.cacheName || "ezw-sdk-cache",
            cacheControl: cacheConfig.cacheControl || "max-age=29",
          })
        );
      }
    } else {
      // Enable compression for other runtimes (if not disabled)
      if (middlewareConfig.compress !== false) {
        this.app.use(compress());
      }
    }
  }

  /**
   * Set up authentication middleware using Hono's bearerAuth
   *
   * This middleware checks for the API key in the Authorization header
   * and verifies that it matches the one provided to the server.
   * The health endpoint is excluded from authentication.
   */
  private setupAuthMiddleware(): void {
    if (!this.apiKey) {
      return;
    }

    // Use Hono's built-in bearerAuth middleware with health endpoint exclusion
    this.app.use("*", async (c, next) => {
      // Skip authentication for health endpoint
      if (c.req.path === "/health") {
        await next();
        return;
      }

      // Apply bearerAuth for all other routes
      const auth = bearerAuth({ token: this.apiKey! });
      await auth(c, next);
    });
  }

  /**
   * Set up routes using modular approach
   */
  private setupModularRoutes(): void {
    // Create server context for routes
    const context: ServerContext = {
      nodes: this.nodes,
      executors: this.executors,
      triggers: this.triggers,
      pages: this.pages,
      modals: this.modals,
      dashboardWidgets: this.dashboardWidgets,
      drawers: this.drawers,
      emailBuilderBlocks: this.emailBuilderBlocks,
      funnelsBuilderBlocks: this.funnelsBuilderBlocks,
      apiKey: this.apiKey,
      permissions: this.permissions,
      options: this.options,
    };

    // Create and mount the router
    const router = createRouter(context);
    this.app.route("/", router);
  }

  /**
   * Get the Hono app instance
   *
   * @returns The Hono app instance
   */
  getApp(): Hono {
    return this.app;
  }

  /**
   * Get the middleware function for the server
   *
   * @returns The middleware function
   */
  getMiddleware() {
    return this.app.fetch;
  }
}
