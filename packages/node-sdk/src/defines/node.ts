/**
 * @file Node definition utilities for ezWorkflow
 *
 * This module provides the core functionality for defining workflow nodes in the ezWorkflow platform.
 * Nodes are the fundamental building blocks of workflows, representing individual operations or tasks
 * that can be connected together to create complex automation flows.
 */

import { v4 as uuidv4 } from "uuid";
import {
  NodeDefinition,
  InputFieldDefinition,
  OutputFieldDefinition,
  ConfigFieldDefinition,
  Node,
} from "@/types/node-types";
import { AuthDefinition } from "@/types/auth-types";
import * as fieldBuilders from "@/builders/fields/field-factories";
import * as authBuilders from "@/builders/auth/auth-factories";
import { componentRegistry } from "../registry";

// Type for field builder functions
type FieldBuilderFunction = (...args: any[]) => { getDefinition(): any };
type FieldBuildersType = Record<string, FieldBuilderFunction>;

/**
 * Parameters for defining a workflow node
 *
 * @interface NodeDefinitionParams
 */
interface NodeDefinitionParams {
  /**
   * Unique identifier for the node. If not provided, a random ID will be generated.
   * @example 'my-custom-node' or 'data-processor-v2'
   */
  id?: string;

  /**
   * Human-readable name for the node that will be displayed in the UI
   * @example 'Data Processor' or 'Email Sender'
   */
  name: string;

  /**
   * Detailed description of what the node does and how to use it
   * @example 'Processes incoming data and transforms it according to specified rules'
   */
  description: string;

  /**
   * Semantic version of the node following semver format
   * @example '1.0.0' or '2.1.3-beta'
   */
  version: string;

  /**
   * Category or categories this node belongs to. Can be a single string or array of strings.
   * Categories help organize nodes in the UI and make them easier to find.
   * @example 'Data Processing' or ['Utilities', 'Text Processing']
   */
  category: string | string[];

  /**
   * Optional tags for additional categorization and searchability
   * @example ['utility', 'transformation', 'api']
   */
  tags?: string[];

  /**
   * Function that defines the input fields for this node.
   * The function receives field builders and should return an object mapping field names to field definitions.
   *
   * @param fields - Object containing all available field builder functions
   * @returns Object mapping input field names to their definitions
   *
   * @example
   * ```typescript
   * inputs: ({ string, number, boolean }) => ({
   *   message: string().description('Message to process').required(),
   *   count: number().description('Number of times to repeat').min(1).max(10),
   *   enabled: boolean().description('Whether processing is enabled').default(true)
   * })
   * ```
   */
  inputs?: (
    fields: FieldBuildersType
  ) => Record<string, { getDefinition(): InputFieldDefinition }>;

  /**
   * Function that defines the output fields for this node.
   * The function receives field builders and should return an object mapping field names to field definitions.
   *
   * @param fields - Object containing all available field builder functions
   * @returns Object mapping output field names to their definitions
   *
   * @example
   * ```typescript
   * outputs: ({ string, object, array }) => ({
   *   result: string().description('Processed result'),
   *   metadata: object({
   *     timestamp: string(),
   *     processingTime: number()
   *   }).description('Processing metadata'),
   *   items: array(string()).description('List of processed items')
   * })
   * ```
   */
  outputs?: (
    fields: typeof fieldBuilders
  ) => Record<
    string,
    ReturnType<(typeof fieldBuilders)[keyof typeof fieldBuilders]>
  >;

  /**
   * Function that defines configuration fields for this node.
   * Configuration fields are set once when the node is added to a workflow and remain constant during execution.
   *
   * @param fields - Object containing all available field builder functions
   * @returns Object mapping config field names to their definitions
   *
   * @example
   * ```typescript
   * config: ({ string, number, options }) => ({
   *   apiEndpoint: string().description('API endpoint URL').required(),
   *   timeout: number().description('Request timeout in seconds').default(30),
   *   method: options([
   *     { label: 'GET', value: 'get' },
   *     { label: 'POST', value: 'post' }
   *   ]).description('HTTP method to use').default('get')
   * })
   * ```
   */
  config?: (
    fields: typeof fieldBuilders
  ) => Record<
    string,
    ReturnType<(typeof fieldBuilders)[keyof typeof fieldBuilders]>
  >;

  /**
   * Authentication configuration for this node.
   * Can be either a static object or a function that uses auth builders to define authentication requirements.
   *
   * @example
   * ```typescript
   * // Static auth configuration
   * auth: {
   *   type: 'bearer',
   *   required: true
   * }
   *
   * // Dynamic auth configuration using builders
   * auth: (builders) => ({
   *   apiKey: builders.apiKey().description('Your API key').required(),
   *   secret: builders.secret().description('Your secret key').required()
   * })
   * ```
   */
  auth?: Record<string, unknown> | ((builders: any) => Record<string, any>);

  /**
   * Additional metadata for the node that can be used by the platform or custom extensions
   * @example { author: 'John Doe', documentation: 'https://docs.example.com/my-node' }
   */
  metadata?: Record<string, unknown>;
}

/**
 * Define a workflow node for the ezWorkflow platform
 *
 * This function creates a new node definition that can be used in workflows. The node definition
 * includes all the metadata, input/output specifications, configuration options, and authentication
 * requirements needed for the node to function properly within the ezWorkflow ecosystem.
 *
 * The function automatically registers the node with the component registry, making it available
 * for use in workflows and discoverable through the platform's APIs.
 *
 * @param params - Configuration object containing all node definition parameters
 * @returns A complete Node object with definition and registration
 *
 * @throws {Error} When field definitions are invalid or malformed
 * @throws {Error} When auth definitions are invalid or malformed
 *
 * @example
 * ```typescript
 * import { defineNode } from '@ezworkflow/node-sdk';
 *
 * // Define a simple text processing node
 * const textProcessor = defineNode({
 *   id: 'text-processor',
 *   name: 'Text Processor',
 *   description: 'Processes text input with various transformation options',
 *   version: '1.0.0',
 *   category: 'Text Processing',
 *   tags: ['text', 'transformation', 'utility'],
 *
 *   inputs: ({ string, options }) => ({
 *     text: string()
 *       .description('Text to process')
 *       .required(),
 *     operation: options([
 *       { label: 'Uppercase', value: 'upper' },
 *       { label: 'Lowercase', value: 'lower' },
 *       { label: 'Title Case', value: 'title' }
 *     ])
 *       .description('Transformation to apply')
 *       .default('upper')
 *   }),
 *
 *   outputs: ({ string, number }) => ({
 *     result: string()
 *       .description('Processed text result'),
 *     length: number()
 *       .description('Length of the processed text')
 *   }),
 *
 *   config: ({ boolean }) => ({
 *     trimWhitespace: boolean()
 *       .description('Whether to trim whitespace before processing')
 *       .default(true)
 *   })
 * });
 * ```
 *
 * @example
 * ```typescript
 * // Define a node with authentication
 * const apiNode = defineNode({
 *   id: 'api-caller',
 *   name: 'API Caller',
 *   description: 'Makes authenticated API calls to external services',
 *   version: '1.0.0',
 *   category: 'API',
 *
 *   inputs: ({ string, object }) => ({
 *     endpoint: string().description('API endpoint').required(),
 *     payload: object().description('Request payload').optional()
 *   }),
 *
 *   outputs: ({ object, number }) => ({
 *     response: object().description('API response'),
 *     statusCode: number().description('HTTP status code')
 *   }),
 *
 *   auth: (builders) => ({
 *     apiKey: builders.apiKey().description('API key for authentication').required()
 *   })
 * });
 * ```
 */
export const defineNode = (params: NodeDefinitionParams): Node => {
  // Generate ID if not provided
  const id = params.id || `node-${Math.random().toString(36).substring(2, 15)}`;

  // Process inputs
  const inputFields: Record<string, InputFieldDefinition> = {};
  if (params.inputs) {
    const inputDefs = params.inputs(fieldBuilders);
    for (const [key, builder] of Object.entries(inputDefs)) {
      if (typeof builder.getDefinition === "function") {
        inputFields[key] = builder.getDefinition() as InputFieldDefinition;
      } else {
        throw new Error(
          `Invalid input field definition for '${key}'. Field builders must have a getDefinition() method.`
        );
      }
    }
  }

  // Process outputs
  const outputFields: Record<string, OutputFieldDefinition> = {};
  if (params.outputs) {
    const outputDefs = params.outputs(fieldBuilders);
    for (const [key, builder] of Object.entries(outputDefs)) {
      if (typeof builder.getDefinition === "function") {
        outputFields[key] = builder.getDefinition() as OutputFieldDefinition;
      } else {
        throw new Error(
          `Invalid output field definition for '${key}'. Field builders must have a getDefinition() method.`
        );
      }
    }
  }

  // Process config
  const configFields: Record<string, ConfigFieldDefinition> = {};
  if (params.config) {
    const configDefs = params.config(fieldBuilders);
    for (const [key, builder] of Object.entries(configDefs)) {
      if (typeof builder.getDefinition === "function") {
        configFields[key] = builder.getDefinition() as ConfigFieldDefinition;
      } else {
        throw new Error(
          `Invalid config field definition for '${key}'. Field builders must have a getDefinition() method.`
        );
      }
    }
  }

  // Process auth
  let authFields: Record<string, unknown> = {};
  if (params.auth) {
    if (typeof params.auth === "function") {
      // Process auth builders
      const authDefs = params.auth(authBuilders);
      for (const [key, builder] of Object.entries(authDefs)) {
        if (typeof builder.getDefinition === "function") {
          authFields[key] = builder.getDefinition();
        } else {
          throw new Error(
            `Invalid auth definition for '${key}'. Auth builders must have a getDefinition() method.`
          );
        }
      }
    } else {
      authFields = params.auth;
    }
  }

  // Create node definition
  const definition: NodeDefinition = {
    id,
    name: params.name,
    description: params.description,
    version: params.version,
    category: params.category,
    tags: params.tags || [],
    inputs: inputFields,
    outputs: outputFields,
    config: configFields,
    auth: authFields,
    metadata: params.metadata || {},
  };

  // Create node
  const node: Node = {
    definition,
  };

  // Register node in the registry
  componentRegistry.registerNode(node);

  // Return node
  return node;
};
